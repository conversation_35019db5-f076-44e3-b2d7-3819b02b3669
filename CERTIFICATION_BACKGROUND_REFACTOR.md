# 产品认证页面背景图重构说明

## 修改概述
移除产品认证页面中的本地资源占位图，改为完全依赖网络请求图片和缓存处理。

## 修改内容

### 1. 代码修改
**文件**: `Nano_Loan/Certification/ProductsCertificationViewController.m`

#### 1.1 认证项背景图处理 (第401-419行)
- **修改前**: 使用本地占位图作为fallback，如 `cell_bg_identity_verified`、`cell_bg_identity_unverified` 等
- **修改后**: 完全依赖接口返回的 `iconURL` (sallyhope字段)
- **改进点**:
  - 移除 `placeholderImage` 参数，设置为 `nil`
  - 添加 `SDWebImageRetryFailed` 选项，提高网络图片加载成功率
  - 添加完成回调处理加载失败情况，设置透明背景避免显示异常
  - 添加详细的日志记录，便于调试

#### 1.2 通知区域背景处理 (第380-389行)
- **修改前**: 使用本地图片 `cell_bg_notice`
- **修改后**: 使用半透明白色背景 `[UIColor colorWithRed:255/255.0 green:255/255.0 blue:255/255.0 alpha:0.8]`
- **预留扩展**: 添加TODO注释，如果后续需要通知背景图，可通过接口下发

#### 1.3 废弃代码清理 (第856-869行)
- 清理了 `cellForRowAtIndexPath` 方法中的本地资源映射代码
- 添加注释说明该方法当前不会被调用（因为 `numberOfRowsInSection` 返回0）
- 为将来可能的使用提供指导注释

### 2. 资源文件删除
删除以下本地图片资源文件夹：
- `cell_bg_identity_verified.imageset`
- `cell_bg_identity_unverified.imageset`
- `cell_bg_personal_verified.imageset`
- `cell_bg_personal_unverified.imageset`
- `cell_bg_job_verified.imageset`
- `cell_bg_job_unverified.imageset`
- `cell_bg_contact_verified.imageset`
- `cell_bg_contact_unverified.imageset`
- `cell_bg_bank_verified.imageset`
- `cell_bg_bank_unverified.imageset`
- `cell_bg_notice.imageset`

### 3. 技术实现细节

#### 3.1 网络图片加载
- 使用 SDWebImage 库进行网络图片加载和缓存
- 配置 `SDWebImageRetryFailed` 选项，在网络不稳定时重试
- 图片加载失败时设置透明背景，保持UI一致性

#### 3.2 接口字段映射
- 认证项背景图: `sallyhope` 字段 → `TrustItem.iconURL` 属性
- 认证项状态: `amused` 字段 → `TrustItem.completed` 属性
- 认证项标题: `coldchicken` 字段 → `TrustItem.title` 属性

#### 3.3 错误处理
- 网络图片加载失败时记录详细日志
- 接口未返回图片URL时记录警告日志
- 设置透明背景作为fallback，避免显示异常

## 优势
1. **减少包体积**: 移除了11个本地图片资源文件
2. **提高灵活性**: 背景图完全由服务端控制，可动态更新
3. **统一管理**: 所有认证项背景图通过接口统一下发和管理
4. **缓存优化**: 利用SDWebImage的缓存机制，提高加载性能
5. **错误处理**: 完善的错误处理和日志记录，便于问题排查

## 注意事项
1. 确保服务端接口正确返回 `sallyhope` 字段的图片URL
2. 图片URL应该是完整的HTTP/HTTPS地址
3. 建议服务端图片使用CDN加速，提高加载速度
4. 如果需要通知区域的背景图，可在接口中添加对应字段

## 测试建议
1. **网络正常情况**: 测试图片正常加载和显示
2. **网络异常情况**: 测试网络断开时的fallback处理
3. **接口异常情况**: 测试接口未返回图片URL时的处理
4. **缓存验证**: 验证图片缓存是否正常工作，重复进入页面时图片应从缓存加载
5. **认证状态**: 检查不同认证状态下的显示效果
6. **日志检查**: 查看控制台日志，确认图片加载状态和错误信息

## 相关接口字段
```json
{
  "trust": [
    {
      "coldchicken": "认证项标题",
      "amused": 1,  // 是否已完成认证 (0/1)
      "sallyhope": "https://example.com/bg_image.png"  // 背景图URL
    }
  ]
}
```
