//
//  IdCardAuthenticationViewController.m
//  Nano_Loan
//
//  业务需求梳理（已完成 ✓ / 待完成 ✗）：
//
//  页面功能概览
//  1. 拉取身份信息接口 Alicia/swallow （✓）
//     • 传参：splendid=产品ID
//     • 解析字段：
//         id_front_msg / face_msg -> 文案
//         youmust / glowing -> 完成状态、图片URL、证件类型
//         beenwaiting / knocking -> 主/备身份证类型数组
//         waydown -> 上传来源控制（0=相册+拍照，1=仅拍照）
//  2. UI 组成
//     • 身份证正面按钮：未完成红色 / 已完成绿色（✓）
//     • 人脸识别按钮：未完成红色 / 已完成绿色（✓）
//
//  实际业务流程与UI进度表:
//  ┌──────────────────────────────────────┬───────┬───────────────────────┐
//  │ 功能点                               │ 状态  │ 说明                  │
//  ├──────────────────────────────────────┼───────┼───────────────────────┤
//  │ 1. 身份证上传流程                    │       │                       │
//  ├──────────────────────────────────────┼───────┼───────────────────────┤
//  │ 1.1 点击身份证正面按钮               │ ✓     │ 已实现基础交互        │
//  │ 1.2 显示身份证类型选择自定义弹窗     │ ✗     │ 替换系统ActionSheet   │
//  │ 1.3 显示相机/相册选择自定义弹窗      │ ✗     │ 替换系统ActionSheet   │
//  │ 1.4 上传照片到服务器                 │ ✓     │ 基于Base64实现        │
//  │ 1.5 身份信息核对自定义弹窗           │ ✗     │ 替换系统Alert         │
//  │ 1.6 自定义日期选择器                 │ ✗     │ 特殊生日选择UI        │
//  │ 1.7 保存身份信息到服务器             │ ✓     │ 已实现基础功能        │
//  │ 1.8 更新身份证状态为已完成           │ ✓     │ 状态图标切换          │
//  ├──────────────────────────────────────┼───────┼───────────────────────┤
//  │ 2. 人脸识别流程                      │       │                       │
//  ├──────────────────────────────────────┼───────┼───────────────────────┤
//  │ 2.1 点击人脸识别按钮                 │ ✓     │ 已实现基础交互        │
//  │ 2.2 显示人脸拍摄指引自定义弹窗       │ ✗     │ 新增功能              │
//  │ 2.3 打开前置摄像头                   │ ✓     │ 已实现基础功能        │
//  │ 2.4 上传人脸照片到服务器             │ ✓     │ 基于Base64实现        │
//  │ 2.5 更新人脸识别状态为已完成         │ ✓     │ 状态图标切换          │
//  ├──────────────────────────────────────┼───────┼───────────────────────┤
//  │ 3. 确认流程                          │       │                       │
//  ├──────────────────────────────────────┼───────┼───────────────────────┤
//  │ 3.1 身份证和人脸都完成后激活确认按钮 │ ✓     │ 按钮状态控制          │
//  │ 3.2 点击确认按钮返回认证首页         │ ✓     │ 已实现基础功能        │
//  │ 3.3 禁止已确认信息再次编辑           │ ✓     │ 已实现状态控制        │
//  └──────────────────────────────────────┴───────┴───────────────────────┘
//
//  核心功能优化事项:
//  1. 身份证类型选择弹窗 - 实现自定义UI替代系统弹窗，展示主选和备选证件类型
//  2. 相机/相册选择弹窗 - 实现带有自定义背景和动效的选择界面
//  3. 身份信息核对弹窗 - 实现可编辑的信息确认界面，包含姓名、证件号、出生日期等
//  4. 自定义日期选择器 - 实现带有特殊背景的生日选择控件
//  5. 人脸拍摄指引弹窗 - 实现指导用户如何正确拍摄人脸的提示页面
//
//  数据流向:
//  1. 页面初始化 -> Alicia/swallow -> 展示UI
//  2. 身份证上传 -> Alicia/everyonecheered -> 展示识别结果
//  3. 保存身份信息 -> Alicia/girlswere -> 更新状态
//  4. 人脸上传 -> Alicia/everyonecheered -> 直接成功提示
//
//  后续 TODO:
//  1. 根据 waydown 动态隐藏相册选项（当前为硬编码）
//  2. 将保存接口实际接入后台（当前仅展示成功提示）
//  3. 上传接口需改为真实 multipart/form-data 而非 Base64
//  4. 上传完成后刷新按钮状态 & 图片预览
//  5. 权限校验与错误提示优化
//  6. 自定义所有弹窗UI组件，替换系统原生组件

//埋点2：开始时间：点击证件上报按钮时间，结束时间：选择完成证件类型时间

#import "IdCardAuthenticationViewController.h"
#import "NetworkManager.h"
#import <MBProgressHUD/MBProgressHUD.h>
#import "IdCardInfo.h"
#import <MobileCoreServices/MobileCoreServices.h>
#import "UIImageView+Web.h"
#import "RiskEventManager.h"
#import "IDTypePickerViewController.h"
#import "PhotoSourcePickerView.h"
#import "FaceGuideViewController.h"
#import "IDInfoEditorViewController.h"
#import <AVFoundation/AVFoundation.h>
#import <Photos/Photos.h>
#import "Popv/IDInfoEditorViewController.h"

// 根据设计稿 375pt 基准宽度的缩放比
#define SCALE_375 (UIScreen.mainScreen.bounds.size.width / 375.0)
// 图片压缩最大大小：800KB
#define MAX_IMAGE_SIZE_KB 800

@interface IdCardAuthenticationViewController () <UIImagePickerControllerDelegate, UINavigationControllerDelegate>
@property (nonatomic, strong) IdCardInfo *info;
@property (nonatomic, copy) NSString *selectedDocType;
@property (nonatomic, assign) NSInteger currentSubject; // 10 face,11 id front
@property (nonatomic, assign) NSInteger currentArrow;   // 1 camera 2 album
@property (nonatomic, strong) UIImageView *frontImageView;
@property (nonatomic, strong) UIButton *confirmButton;
@property (nonatomic, assign) NSTimeInterval pageStartTime;
@property (nonatomic, strong) UIView *navView; // 自定义导航栏
@property (nonatomic, strong) UIImageView *contentBackgroundView;
@property (nonatomic, strong) UIImageView *frontStatusView;
@property (nonatomic, strong) UIImageView *faceStatusView;
@property (nonatomic, strong) UILabel *headerLabel; // 用于显示身份证类型的标签
@property (nonatomic, assign) NSTimeInterval docSelectStartTime; // 埋点2开始时间
// 新增埋点3、4开始时间
@property (nonatomic, assign) NSTimeInterval docFrontStartTime; // 埋点3开始时间
@property (nonatomic, assign) NSTimeInterval faceGuideStartTime; // 埋点4开始时间
@end

@implementation IdCardAuthenticationViewController {
    BOOL _didInitialLoad;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    // 添加透明背景图
    [self setupBackgroundImage];
    self.view.backgroundColor = [UIColor systemBackgroundColor];
    // 自定义导航栏
    [self setupCustomNavBar];
    // 保持原有页面标题逻辑用于无障碍
    // self.title = self.editMode ? @"Edit ID" : @"ID Authentication";

    self.pageStartTime = [[NSDate date] timeIntervalSince1970];

    [self fetchIdInfo];
    _didInitialLoad = YES;
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    [self.navigationController setNavigationBarHidden:YES animated:animated];
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
    [self.navigationController setNavigationBarHidden:NO animated:animated];
}

#pragma mark - 自定义导航栏
- (void)setupCustomNavBar {
    self.navView = [[UIView alloc] init];
    self.navView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.view addSubview:self.navView];
    UILayoutGuide *safe = self.view.safeAreaLayoutGuide;
    [NSLayoutConstraint activateConstraints:@[
        [self.navView.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor],
        [self.navView.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor],
        [self.navView.topAnchor constraintEqualToAnchor:safe.topAnchor],
        [self.navView.heightAnchor constraintEqualToConstant:44]
    ]];

    UIButton *backBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    [backBtn setImage:[UIImage imageNamed:@"nav_back"] forState:UIControlStateNormal];
    backBtn.frame = CGRectMake(0, 0, 34, 34);
    backBtn.translatesAutoresizingMaskIntoConstraints = NO;
    [backBtn addTarget:self action:@selector(backAction) forControlEvents:UIControlEventTouchUpInside];
    [self.navView addSubview:backBtn];
    [NSLayoutConstraint activateConstraints:@[
        [backBtn.leadingAnchor constraintEqualToAnchor:self.navView.leadingAnchor constant:16],
        [backBtn.centerYAnchor constraintEqualToAnchor:self.navView.centerYAnchor],
        [backBtn.widthAnchor constraintEqualToConstant:34],
        [backBtn.heightAnchor constraintEqualToConstant:34]
    ]];

    UILabel *titleLabel = [[UILabel alloc] init];
    // 标题与认证首页保持一致
    titleLabel.text = @"Identity Authentication";
    titleLabel.font = [UIFont boldSystemFontOfSize:22];
    titleLabel.textColor = [UIColor whiteColor];
    titleLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [self.navView addSubview:titleLabel];
    [NSLayoutConstraint activateConstraints:@[
        [titleLabel.centerXAnchor constraintEqualToAnchor:self.navView.centerXAnchor],
        [titleLabel.centerYAnchor constraintEqualToAnchor:self.navView.centerYAnchor]
    ]];
}

- (void)backAction {
    [self.navigationController popViewControllerAnimated:YES];
}

- (void)setupBackgroundImage {
    UIImageView *bgView = [[UIImageView alloc] initWithFrame:self.view.bounds];
    bgView.image = [UIImage imageNamed:@"general_background"];
    bgView.contentMode = UIViewContentModeScaleAspectFill;
    bgView.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;
    bgView.userInteractionEnabled = NO;
    bgView.alpha = 1.0; // 可根据需要调整透明度
    [self.view insertSubview:bgView atIndex:0];
}

- (void)fetchIdInfo {
    MBProgressHUD *hud = [MBProgressHUD showHUDAddedTo:self.view animated:YES];
    hud.label.text = @"Loading…";
    __weak typeof(self) weakSelf = self;
    NSDictionary *params = self.splendid ? @{ @"splendid": self.splendid } : @{};
    [NetworkManager requestWithAPI:@"Alicia/swallow" params:params method:@"GET" completion:^(NSDictionary *response, NSError *error) {
        dispatch_async(dispatch_get_main_queue(), ^{
            [hud hideAnimated:YES];
            if (error) {
                [MBProgressHUD showHUDAddedTo:weakSelf.view animated:YES].label.text = error.localizedDescription ?: @"Error";
                return;
            }
            NSDictionary *awkward = response[@"awkward"];
            if (![awkward isKindOfClass:[NSDictionary class]]) return;
            weakSelf.info = [[IdCardInfo alloc] initWithDictionary:awkward];
            
            // 打印解析到的图片URL，方便调试
            NSLog(@"解析到的身份证图片URL: %@", weakSelf.info.frontImageURL);
            NSLog(@"解析到的人脸图片URL: %@", weakSelf.info.faceImageURL);
            
            [weakSelf buildUI];
            [weakSelf updateConfirmState];
        });
    }];
}

- (void)buildUI {
    // 清理旧视图，避免重复叠加
    [self.contentBackgroundView removeFromSuperview];
    self.contentBackgroundView = nil;
    [self.confirmButton removeFromSuperview];
    self.confirmButton = nil;

    // 1. 创建承载背景图
    UIImageView *panel = [[UIImageView alloc] init];
    panel.translatesAutoresizingMaskIntoConstraints = NO;
    panel.userInteractionEnabled = YES; // 内部需要响应按钮事件
    panel.image = [UIImage imageNamed:@"idAuthPanelBg"];
    [self.view addSubview:panel];
    self.contentBackgroundView = panel;

    // 347 * 490 设计稿 => 高度 = 宽度 * 490/347
    CGFloat ratio = 490.0 / 347.0;
    [NSLayoutConstraint activateConstraints:@[
        [panel.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor constant:14],
        [panel.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor constant:-14],
        [panel.topAnchor constraintEqualToAnchor:self.navView.bottomAnchor constant:20],
        [panel.heightAnchor constraintEqualToAnchor:panel.widthAnchor multiplier:ratio]
    ]];

    // 按钮统一参数
    CGFloat cellHeight = 153.0 * SCALE_375;
    CGFloat sidePadding = 12.0; // 原来16+12，保持不变

    // 创建顶部步骤号与说明
    UILabel *stepNumLabel = [[UILabel alloc] init];
    stepNumLabel.translatesAutoresizingMaskIntoConstraints = NO;
    stepNumLabel.text = @"1";
    stepNumLabel.font = [UIFont fontWithName:@"Verdana-BoldItalic" size:40];
    stepNumLabel.textColor = [UIColor colorWithRed:160/255.0 green:233/255.0 blue:234/255.0 alpha:1.0];
    [panel addSubview:stepNumLabel];

    UILabel *stepTitleLabel = [[UILabel alloc] init];
    stepTitleLabel.translatesAutoresizingMaskIntoConstraints = NO;
    stepTitleLabel.numberOfLines = 2;
    stepTitleLabel.text = @"Verified Identity\nRegistration"; // 根据设计图分两行
    stepTitleLabel.font = [UIFont fontWithName:@"Verdana-BoldItalic" size:14];
    stepTitleLabel.textColor = UIColor.blackColor;
    [panel addSubview:stepTitleLabel];

    [NSLayoutConstraint activateConstraints:@[
        [stepNumLabel.leadingAnchor constraintEqualToAnchor:panel.leadingAnchor constant:14],
        [stepNumLabel.topAnchor constraintEqualToAnchor:panel.topAnchor constant:14],
        [stepTitleLabel.leadingAnchor constraintEqualToAnchor:stepNumLabel.trailingAnchor constant:8],
        [stepTitleLabel.centerYAnchor constraintEqualToAnchor:stepNumLabel.centerYAnchor]
    ]];

    // 头部提示背景 90pt 高度
    UIImageView *headerBg = [[UIImageView alloc] init];
    headerBg.translatesAutoresizingMaskIntoConstraints = NO;
    headerBg.userInteractionEnabled = YES;
    headerBg.image = [UIImage imageNamed:@"idHeaderCardBg"]; // TODO: 替换为实际切图名称
    headerBg.layer.cornerRadius = 12.0;
    headerBg.clipsToBounds = YES;
    [panel addSubview:headerBg];

    [NSLayoutConstraint activateConstraints:@[
        [headerBg.leadingAnchor constraintEqualToAnchor:panel.leadingAnchor constant:sidePadding],
        [headerBg.trailingAnchor constraintEqualToAnchor:panel.trailingAnchor constant:-sidePadding],
        [headerBg.topAnchor constraintEqualToAnchor:stepNumLabel.bottomAnchor constant:10],
        [headerBg.heightAnchor constraintEqualToConstant:73 * SCALE_375]
    ]];

    // headerBg 内部内容：icon + 文案 + 箭头
    UIImageView *headerIcon = [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"idHeaderIcon"]]; // TODO: 替换为实际切图名称
    headerIcon.translatesAutoresizingMaskIntoConstraints = NO;
    [headerBg addSubview:headerIcon];

    self.headerLabel = [[UILabel alloc] init];
    self.headerLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [self updateHeaderLabelText]; // 使用新方法更新文本
    self.headerLabel.font = [UIFont systemFontOfSize:16 weight:UIFontWeightSemibold];
    self.headerLabel.textColor = UIColor.whiteColor;
    [headerBg addSubview:self.headerLabel];

    UIImageView *arrowView = [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"setting_cell_arrow"]];
    arrowView.translatesAutoresizingMaskIntoConstraints = NO;
    [headerBg addSubview:arrowView];

    [NSLayoutConstraint activateConstraints:@[
        [headerIcon.leadingAnchor constraintEqualToAnchor:headerBg.leadingAnchor constant:12],
        [headerIcon.centerYAnchor constraintEqualToAnchor:headerBg.centerYAnchor],
        [headerIcon.widthAnchor constraintEqualToConstant:47],
        [headerIcon.heightAnchor constraintEqualToConstant:30],

        [self.headerLabel.leadingAnchor constraintEqualToAnchor:headerIcon.trailingAnchor constant:12],
        [self.headerLabel.centerYAnchor constraintEqualToAnchor:headerBg.centerYAnchor],

        [arrowView.trailingAnchor constraintEqualToAnchor:headerBg.trailingAnchor constant:-12],
        [arrowView.centerYAnchor constraintEqualToAnchor:headerBg.centerYAnchor],
        [arrowView.widthAnchor constraintEqualToConstant:18],
        [arrowView.heightAnchor constraintEqualToConstant:18]
    ]];

    // 2. 身份证正面按钮
    UIButton *frontBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    frontBtn.translatesAutoresizingMaskIntoConstraints = NO;
    frontBtn.backgroundColor = UIColor.whiteColor;
    frontBtn.layer.cornerRadius = 12.0;
    frontBtn.tag = 1001;
    [frontBtn addTarget:self action:@selector(photoButtonTapped:) forControlEvents:UIControlEventTouchUpInside];
    [panel addSubview:frontBtn];
    [NSLayoutConstraint activateConstraints:@[
        [frontBtn.leadingAnchor constraintEqualToAnchor:panel.leadingAnchor constant:sidePadding],
        [frontBtn.trailingAnchor constraintEqualToAnchor:panel.trailingAnchor constant:-sidePadding],
        [frontBtn.heightAnchor constraintEqualToConstant:cellHeight],
        [frontBtn.topAnchor constraintEqualToAnchor:headerBg.bottomAnchor constant:10]
    ]];

    // 内部图片 (身份证/预览)
    self.frontImageView = [[UIImageView alloc] init];
    self.frontImageView.translatesAutoresizingMaskIntoConstraints = NO;
    self.frontImageView.contentMode = UIViewContentModeScaleAspectFill;
    self.frontImageView.clipsToBounds = YES; // 添加裁剪，防止图片溢出
    
    // 先设置默认图片，确保始终有图片显示
    self.frontImageView.image = [UIImage imageNamed:@"idDocIcon"];
    self.frontImageView.backgroundColor = [UIColor colorWithWhite:1 alpha:0.2];
    
    // 仅当有URL时才加载网络图片
    if (self.info.frontImageURL.length > 0) {
        NSLog(@"加载身份证图片: %@", self.info.frontImageURL);
        [self.frontImageView setImageWithURLString:self.info.frontImageURL placeholderImage:[UIImage imageNamed:@"idDocIcon"]];
    }
    [frontBtn addSubview:self.frontImageView];
    [NSLayoutConstraint activateConstraints:@[
        [self.frontImageView.centerXAnchor constraintEqualToAnchor:frontBtn.centerXAnchor],
        [self.frontImageView.centerYAnchor constraintEqualToAnchor:frontBtn.centerYAnchor],
        [self.frontImageView.widthAnchor constraintEqualToConstant:113 * SCALE_375],
        [self.frontImageView.heightAnchor constraintEqualToConstant:70 * SCALE_375]
    ]];

    // 状态覆盖图
    self.frontStatusView = [[UIImageView alloc] initWithImage:[UIImage imageNamed:(self.info.frontCompleted ? @"statusDone" : @"statusPending")]];
    self.frontStatusView.translatesAutoresizingMaskIntoConstraints = NO;
    [frontBtn addSubview:self.frontStatusView];
    [NSLayoutConstraint activateConstraints:@[
        [self.frontStatusView.centerXAnchor constraintEqualToAnchor:self.frontImageView.centerXAnchor],
        [self.frontStatusView.centerYAnchor constraintEqualToAnchor:self.frontImageView.centerYAnchor],
        [self.frontStatusView.widthAnchor constraintEqualToConstant:32],
        [self.frontStatusView.heightAnchor constraintEqualToConstant:32]
    ]];

    // 为按钮设置标题(辅助无障碍)
    NSString *frontTitle = self.info.frontMsg.length > 0 ? self.info.frontMsg : @"ID Front";
    [frontBtn setAccessibilityLabel:frontTitle];

    // 3. 人脸识别按钮
    UIButton *faceBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    faceBtn.translatesAutoresizingMaskIntoConstraints = NO;
    faceBtn.backgroundColor = UIColor.whiteColor;
    faceBtn.layer.cornerRadius = 12.0;
    faceBtn.tag = 1002;
    [faceBtn addTarget:self action:@selector(photoButtonTapped:) forControlEvents:UIControlEventTouchUpInside];
    [panel addSubview:faceBtn];
    [NSLayoutConstraint activateConstraints:@[
        [faceBtn.leadingAnchor constraintEqualToAnchor:frontBtn.leadingAnchor],
        [faceBtn.trailingAnchor constraintEqualToAnchor:frontBtn.trailingAnchor],
        [faceBtn.heightAnchor constraintEqualToConstant:cellHeight],
        [faceBtn.bottomAnchor constraintEqualToAnchor:panel.bottomAnchor constant:-16]
    ]];

    // now finalize frontBtn vertical position relative to faceBtn
    [NSLayoutConstraint activateConstraints:@[
        [frontBtn.bottomAnchor constraintEqualToAnchor:faceBtn.topAnchor constant:-14]
    ]];

    UIImageView *faceIconView = [[UIImageView alloc] init];
    faceIconView.translatesAutoresizingMaskIntoConstraints = NO;
    faceIconView.contentMode = UIViewContentModeScaleAspectFill;
    faceIconView.clipsToBounds = YES; // 添加裁剪，防止图片溢出
    
    // 先设置默认图片，确保始终有图片显示
    faceIconView.image = [UIImage imageNamed:@"faceIcon"];
    faceIconView.backgroundColor = [UIColor colorWithWhite:1 alpha:0.2];
    
    // 仅当有URL时才加载网络图片
    if (self.info.faceImageURL.length > 0) {
        NSLog(@"加载人脸图片: %@", self.info.faceImageURL);
        [faceIconView setImageWithURLString:self.info.faceImageURL placeholderImage:[UIImage imageNamed:@"faceIcon"]];
    }
    [faceBtn addSubview:faceIconView];
    [NSLayoutConstraint activateConstraints:@[
        [faceIconView.centerXAnchor constraintEqualToAnchor:faceBtn.centerXAnchor],
        [faceIconView.centerYAnchor constraintEqualToAnchor:faceBtn.centerYAnchor],
        [faceIconView.widthAnchor constraintEqualToConstant:113 * SCALE_375],
        [faceIconView.heightAnchor constraintEqualToConstant:70 * SCALE_375]
    ]];

    self.faceStatusView = [[UIImageView alloc] initWithImage:[UIImage imageNamed:(self.info.faceCompleted ? @"statusDone" : @"statusPending")]];
    self.faceStatusView.translatesAutoresizingMaskIntoConstraints = NO;
    [faceBtn addSubview:self.faceStatusView];
    [NSLayoutConstraint activateConstraints:@[
        [self.faceStatusView.centerXAnchor constraintEqualToAnchor:faceIconView.centerXAnchor],
        [self.faceStatusView.centerYAnchor constraintEqualToAnchor:faceIconView.centerYAnchor],
        [self.faceStatusView.widthAnchor constraintEqualToConstant:32],
        [self.faceStatusView.heightAnchor constraintEqualToConstant:32]
    ]];

    NSString *faceTitle = self.info.faceMsg.length > 0 ? self.info.faceMsg : @"Face Recognition";
    [faceBtn setAccessibilityLabel:faceTitle];

    // 4. Confirm 按钮 (放在 panel 下方)
    self.confirmButton = [UIButton buttonWithType:UIButtonTypeCustom];
    self.confirmButton.translatesAutoresizingMaskIntoConstraints = NO;
    self.confirmButton.layer.cornerRadius = 25;
    self.confirmButton.clipsToBounds = YES;
    [self.confirmButton setBackgroundImage:[UIImage imageNamed:@"home_apply_bg"] forState:UIControlStateNormal];
    [self.confirmButton setBackgroundImage:[UIImage imageNamed:@"home_apply_bg"] forState:UIControlStateDisabled];
    [self.confirmButton setTitle:@"Next" forState:UIControlStateNormal];
    [self.confirmButton setTitleColor:UIColor.whiteColor forState:UIControlStateNormal];
    self.confirmButton.titleLabel.font = [UIFont fontWithName:@"Verdana-BoldItalic" size:22.0f];
    [self.confirmButton addTarget:self action:@selector(confirmTapped) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:self.confirmButton];

    [NSLayoutConstraint activateConstraints:@[
        [self.confirmButton.leadingAnchor constraintEqualToAnchor:panel.leadingAnchor],
        [self.confirmButton.trailingAnchor constraintEqualToAnchor:panel.trailingAnchor],
        [self.confirmButton.topAnchor constraintEqualToAnchor:panel.bottomAnchor constant:40],
        [self.confirmButton.heightAnchor constraintEqualToConstant:50]
    ]];

    [self updateConfirmState];
}

- (void)updateConfirmState {
    BOOL ready = self.info.frontCompleted && self.info.faceCompleted;
    // 按钮始终可点击，使用透明度区分状态
    self.confirmButton.enabled = YES;
    self.confirmButton.alpha = 1.0; // 未完成时半透明提示

    // 更新状态图标
    self.frontStatusView.image = [UIImage imageNamed:(self.info.frontCompleted ? @"statusDone" : @"statusPending")];
    self.faceStatusView.image  = [UIImage imageNamed:(self.info.faceCompleted  ? @"statusDone" : @"statusPending")];
}

#pragma mark - Actions

- (void)photoButtonTapped:(UIButton *)sender {
    if (sender.tag == 1001) {
        if (!self.info.frontCompleted) {
            // 记录埋点2开始时间（点击证件上报按钮时间）
            self.docSelectStartTime = [[NSDate date] timeIntervalSince1970];
            [self showIDTypePicker];
        }
    } else {
        // Face: camera only
        if (!self.info.frontCompleted) {
            // 如果身份证尚未完成，先引导用户完成身份证流程
            UIButton *frontBtn = [self.view viewWithTag:1001];
            if (frontBtn) {
                [self photoButtonTapped:frontBtn];
            }
            return;
        }

        if (!self.info.faceCompleted) {
            self.currentSubject = 10;
            self.selectedDocType = nil;
            // 记录埋点4开始时间（点击人脸出现弹窗时间）
            self.faceGuideStartTime = [[NSDate date] timeIntervalSince1970];
            
            // 使用自定义的人脸识别引导弹窗
            __weak typeof(self) weakSelf = self;
            FaceGuideViewController *faceGuide = [[FaceGuideViewController alloc] initWithCompletion:^{
                // 引导弹窗关闭后打开相机
                if ([UIImagePickerController isSourceTypeAvailable:UIImagePickerControllerSourceTypeCamera]) {
                    weakSelf.currentArrow = 1; // 设置为相机来源
                    UIImagePickerController *picker = [[UIImagePickerController alloc] init];
                    picker.sourceType = UIImagePickerControllerSourceTypeCamera;
                    picker.cameraDevice = UIImagePickerControllerCameraDeviceFront; // 使用前置摄像头
                    picker.delegate = weakSelf;
                    [weakSelf presentViewController:picker animated:YES completion:nil];
                } else {
                    // 如果相机不可用，降级到 sheet
                    [weakSelf presentPhotoOptionsAllowAlbum:NO completion:^(UIImage * _Nonnull image) {
                        [weakSelf uploadImage:image];
                    }];
                }
            }];
            
            // 确保弹窗显示在最上层
            UIWindow *keyWindow = [UIApplication sharedApplication].windows.firstObject;
            if (keyWindow) {
                [faceGuide showInView:keyWindow animated:YES];
            } else {
                [faceGuide showInView:self.view animated:YES];
            }
        }
    }
}

- (void)presentPhotoOptionsAllowAlbum:(BOOL)allowAlbum completion:(void (^)(UIImage *image))completion {
    // 使用自定义的相机/相册选择弹窗，替换系统UIAlertController
    PhotoSourcePickerView *pickerView = [[PhotoSourcePickerView alloc] initWithAllowAlbum:allowAlbum completion:^(PhotoSourceType selectedType) {
        self.currentArrow = selectedType; // 1 = camera, 2 = album
        if (selectedType == PhotoSourceTypeCamera) {
            [self checkCameraAuthorization:^(BOOL granted) {
                if (granted) {
                    [self presentImagePickerWithSource:UIImagePickerControllerSourceTypeCamera];
                } else {
                    [self showPermissionAlertWithTitle:@"Camera Permission Denied" message:@"Please enable camera access in Settings to continue." ];
                }
            }];
        } else {
            [self checkPhotoLibraryAuthorization:^(BOOL granted) {
                if (granted) {
                    [self presentImagePickerWithSource:UIImagePickerControllerSourceTypePhotoLibrary];
                } else {
                    [self showPermissionAlertWithTitle:@"Photo Library Permission Denied" message:@"Please enable photo library access in Settings to continue." ];
                }
            }];
        }
    }];
    
    // 确保弹窗显示在最上层
    UIWindow *keyWindow = [UIApplication sharedApplication].windows.firstObject;
    if (keyWindow) {
        [pickerView showInView:keyWindow animated:YES];
    } else {
        [pickerView showInView:self.view animated:YES];
    }
}

#pragma mark - UIImagePickerControllerDelegate
- (void)imagePickerController:(UIImagePickerController *)picker didFinishPickingMediaWithInfo:(NSDictionary<UIImagePickerControllerInfoKey,id> *)info {
    UIImage *image = info[UIImagePickerControllerOriginalImage];
    [picker dismissViewControllerAnimated:YES completion:^{
        [self uploadImage:image];
    }];
}

- (void)imagePickerControllerDidCancel:(UIImagePickerController *)picker {
    [picker dismissViewControllerAnimated:YES completion:nil];
}

- (void)showIDTypePicker {
    // 收集可选证件类型
    NSArray<NSString *> *primary = self.info.primaryIDs ?: @[];
    NSArray<NSString *> *secondary = self.info.secondaryIDs ?: @[];
    if (primary.count == 0 && secondary.count == 0) {
        // 如果没有类型可选，直接进入拍照
        self.currentSubject = 11;
        self.docFrontStartTime = [[NSDate date] timeIntervalSince1970]; // 记录埋点3开始时间
        [self presentPhotoOptionsAllowAlbum:YES completion:^(UIImage * _Nonnull image) {}];
        return;
    }

    // 使用自定义弹窗页面进行选择
    IDTypePickerViewController *picker = [[IDTypePickerViewController alloc] initWithPrimaryIDs:primary
                                                                                 secondaryIDs:secondary
                                                                              selectionHandler:^(NSString * _Nonnull docType) {
        // 记录埋点2结束时间（选择完成证件类型时间）并上报
        NSTimeInterval endTime = [[NSDate date] timeIntervalSince1970];
        if (self.docSelectStartTime > 0) {
            NSLog(@"🔥 [埋点2-证件类型选择] 开始上报 startTime:%.0f endTime:%.0f", self.docSelectStartTime, endTime);
            [RiskEventManager reportEventType:RiskEventTypeDocumentSelect
                                   startTime:self.docSelectStartTime
                                     endTime:endTime
                                     orderId:nil];
            // 重置开始时间避免重复上报
            self.docSelectStartTime = 0;
        }

        self.selectedDocType = docType;
        self.currentSubject = 11;
        self.docFrontStartTime = [[NSDate date] timeIntervalSince1970]; // 记录埋点3开始时间
        [self presentPhotoOptionsAllowAlbum:YES completion:^(UIImage * _Nonnull image) {
            [self uploadImage:image];
        }];
    }];
    
    // 设置正确的呈现样式
    picker.modalPresentationStyle = UIModalPresentationOverFullScreen;
    picker.modalTransitionStyle = UIModalTransitionStyleCrossDissolve;
    
    [self presentViewController:picker animated:YES completion:nil];
}

- (void)uploadImage:(UIImage *)image {
    MBProgressHUD *hud = [MBProgressHUD showHUDAddedTo:self.view animated:YES];
    hud.label.text = @"Uploading…";

    // 使用multipart/form-data替代Base64编码
    NSMutableDictionary *params = [@{ @"arrow": @(self.currentArrow),
                                     @"subject": @(self.currentSubject)
                                   } mutableCopy];
    if (self.selectedDocType) params[@"withcolour"] = self.selectedDocType;
    
    // 压缩图片，确保大小不超过800KB
    NSData *imageData = [self compressImageToMaxSize:image maxSizeKB:MAX_IMAGE_SIZE_KB];
    
    if (!imageData) {
        dispatch_async(dispatch_get_main_queue(), ^{
            [hud hideAnimated:YES];
            MBProgressHUD *errHud = [MBProgressHUD showHUDAddedTo:self.view animated:YES];
            errHud.mode = MBProgressHUDModeText;
            errHud.label.text = @"Failed to process image";
            [errHud hideAnimated:YES afterDelay:1.5];
        });
        return;
    }
    
    __weak typeof(self) weakSelf = self;
    [NetworkManager uploadImageWithAPI:@"Alicia/everyonecheered" 
                                params:params 
                              imageKey:@"doorway" 
                             imageData:imageData 
                            completion:^(NSDictionary *response, NSError *error) {
        dispatch_async(dispatch_get_main_queue(), ^{
            [hud hideAnimated:YES];
            if (error) {
                MBProgressHUD *err = [MBProgressHUD showHUDAddedTo:weakSelf.view animated:YES];
                err.mode = MBProgressHUDModeText;
                err.label.text = error.localizedDescription ?: @"Upload failed";
                [err hideAnimated:YES afterDelay:1.5];
                // 上传失败时更新按钮状态
                [weakSelf updateConfirmState];
                return;
            }
            
            NSLog(@"上传响应: %@", response);
            
            if (weakSelf.currentSubject == 11) {
                // 身份证正面
                NSDictionary *awk = response[@"awkward"];
                NSString *imageUrl = awk[@"toteach"];
                if (imageUrl.length > 0) {
                    // 更新图片URL到info对象
                    weakSelf.info.frontImageURL = imageUrl;
                    NSLog(@"更新身份证图片URL: %@", imageUrl);
                }
                
                // 直接设置本地图片而不是加载网络图片，避免延迟
                weakSelf.frontImageView.image = image;
                weakSelf.frontImageView.backgroundColor = UIColor.clearColor;
                
                NSArray *sizes = awk[@"sizes"];
                if ([sizes isKindOfClass:[NSArray class]]) {
                    [weakSelf showEditFieldsWithSizes:sizes];
                }
            } else {
                // 人脸照片
                NSDictionary *awk = response[@"awkward"];
                NSString *imageUrl = awk[@"toteach"];
                if (imageUrl.length > 0) {
                    // 更新图片URL到info对象
                    weakSelf.info.faceImageURL = imageUrl;
                    NSLog(@"更新人脸图片URL: %@", imageUrl);
                }
                
                // 人脸上传成功，直接更新状态和图片
                weakSelf.info.faceCompleted = YES;
                
                // 使用本地图片直接更新UI，避免重新加载
                UIImageView *faceImageView = [weakSelf findFaceImageView];
                if (faceImageView) {
                    faceImageView.image = image;
                    faceImageView.backgroundColor = UIColor.clearColor;
                }
                weakSelf.faceStatusView.image = [UIImage imageNamed:@"statusDone"];
                
                // 上报埋点4（人脸识别）
                NSTimeInterval endTime = [[NSDate date] timeIntervalSince1970];
                NSTimeInterval start = weakSelf.faceGuideStartTime > 0 ? weakSelf.faceGuideStartTime : weakSelf.pageStartTime;
                NSLog(@"🎯 [埋点4-人脸识别] 开始上报 startTime:%.0f endTime:%.0f", start, endTime);
                [RiskEventManager reportEventType:RiskEventTypeFaceRecognition
                                       startTime:start
                                         endTime:endTime
                                         orderId:nil];
                weakSelf.faceGuideStartTime = 0; // 重置，防止重复上报
                
                MBProgressHUD *ok = [MBProgressHUD showHUDAddedTo:weakSelf.view animated:YES];
                ok.mode = MBProgressHUDModeText;
                ok.label.text = @"Face upload success";
                [ok hideAnimated:YES afterDelay:1.2];
               
                // 重新拉取身份信息，确保状态正确更新
                [weakSelf fetchIdInfo];
            }
            
        });
    }];
}

// 辅助方法：查找人脸图片视图
- (UIImageView *)findFaceImageView {
    UIButton *faceBtn = [self.view viewWithTag:1002];
    if (!faceBtn) return nil;
    
    for (UIView *subview in faceBtn.subviews) {
        if ([subview isKindOfClass:[UIImageView class]] && subview != self.faceStatusView) {
            return (UIImageView *)subview;
        }
    }
    return nil;
}

- (void)showEditFieldsWithSizes:(NSArray<NSDictionary *> *)sizes {
    // 使用自定义的身份信息编辑弹窗
    __weak typeof(self) weakSelf = self;
    __block IDInfoEditorViewController *editor = [[IDInfoEditorViewController alloc] initWithInfoItems:sizes completion:^(NSDictionary *editedInfo, BOOL isCancelled) {
        if (!isCancelled && editedInfo) {
            NSLog(@"Edited info ready to upload: %@", editedInfo);
            NSMutableDictionary *params = [editedInfo mutableCopy];
            params[@"subject"] = @11;
            params[@"withcolour"] = weakSelf.selectedDocType ?: @"";

            // 统一使用全局窗口显示HUD，避免出现两个不同的HUD
            UIView *hudHost = UIApplication.sharedApplication.windows.firstObject ?: weakSelf.view;
            MBProgressHUD *hud = [MBProgressHUD showHUDAddedTo:hudHost animated:YES];
            hud.label.text = @"Saving…";
            hud.layer.zPosition = 2000; // 确保在弹窗之上
            [hudHost bringSubviewToFront:hud];

            [NetworkManager postFormWithAPI:@"Alicia/girlswere" params:params completion:^(NSDictionary *response, NSError *error) {
                dispatch_async(dispatch_get_main_queue(), ^{
                    [hud hideAnimated:YES];
                    BOOL apiSuccess = !error && ([[response objectForKey:@"modest"] integerValue] == 0);
                    NSString *hudText;
                    if (error) {
                        hudText = error.localizedDescription ?: @"Save failed";
                        NSLog(@"API Error: %@", error.localizedDescription);
                    } else if (!apiSuccess) {
                        NSString *patted = response[@"patted"];
                        hudText = patted.length > 0 ? patted : @"Save failed";
                        NSLog(@"API Failed: %@, Response: %@", hudText, response);
                    } else {
                        hudText = @"Save success";
                        NSLog(@"API Success: %@", response);
                    }

                    MBProgressHUD *toast = [MBProgressHUD showHUDAddedTo:hudHost animated:YES];
                    toast.mode = MBProgressHUDModeText;
                    toast.label.text = hudText;
                    toast.layer.zPosition = 2000;
                    [hudHost bringSubviewToFront:toast];
                    [toast hideAnimated:YES afterDelay:1.5];

                    if (apiSuccess) {
                        weakSelf.info.frontCompleted = YES;
                        weakSelf.frontStatusView.image = [UIImage imageNamed:@"statusDone"];
                        [editor dismissAnimated:YES];
                        NSTimeInterval endTime = [[NSDate date] timeIntervalSince1970];
                        if (weakSelf.docFrontStartTime > 0) {
                            NSLog(@"📄 [埋点3-证件正面] 开始上报 startTime:%.0f endTime:%.0f", weakSelf.docFrontStartTime, endTime);
                            [RiskEventManager reportEventType:RiskEventTypeDocumentFront
                                                   startTime:weakSelf.docFrontStartTime
                                                     endTime:endTime
                                                     orderId:nil];
                            weakSelf.docFrontStartTime = 0;
                        }
                        // 移除埋点5的重复上报 - 证件保存成功后只上报埋点3
                        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                            [weakSelf fetchIdInfo];
                        });
                    }
                });
            }];
        }
    }];
    UIWindow *keyWindow = [UIApplication sharedApplication].windows.firstObject;
    if (keyWindow) {
        [editor showInView:keyWindow animated:YES];
    } else {
        [editor showInView:self.view animated:YES];
    }
}
    
- (void)confirmTapped {
    NSLog(@"[IdAuth] Next button tapped. enabled=%@, frontCompleted=%@, faceCompleted=%@", self.confirmButton.enabled ? @"YES" : @"NO", self.info.frontCompleted ? @"YES" : @"NO", self.info.faceCompleted ? @"YES" : @"NO");
    // 判断完成状态
    if (!self.info.frontCompleted) {
        // 引导用户先完成身份证上传
        UIButton *frontBtn = [self.view viewWithTag:1001];
        if (frontBtn) {
            [self photoButtonTapped:frontBtn];
        }
        return;
    }

    if (!self.info.faceCompleted) {
        // 引导用户完成人脸上传
        UIButton *faceBtn = [self.view viewWithTag:1002];
        if (faceBtn) {
            [self photoButtonTapped:faceBtn];
        }
        return;
    }

    // 两项都完成，返回上一页面
    NSTimeInterval endTime = [[NSDate date] timeIntervalSince1970];
    NSLog(@"✅ [埋点5-身份认证完成] 开始上报 startTime:%.0f endTime:%.0f", self.pageStartTime, endTime);
    [RiskEventManager reportEventType:RiskEventTypePersonalInfo
                           startTime:self.pageStartTime
                             endTime:endTime
                             orderId:nil];

    [self.navigationController popViewControllerAnimated:YES];
}

#pragma mark - 待完成的自定义组件

/*
 * 待完成组件清单与实现规划:
 *
 * 1. PhotoSourcePickerView (相机/相册选择UI)
 *    - 文件: PhotoSourcePickerView.h/.m
 *    - 功能: 替换系统ActionSheet，展示相机和相册选项
 *    - 交互: 点击后回调对应的选择结果
 *    - 特性: 支持根据waydown参数动态隐藏相册选项
 *    - 样式: 使用应用统一背景和按钮样式
 *
 * 2. FaceGuideViewController (人脸拍摄引导)
 *    - 文件: FaceGuideViewController.h/.m
 *    - 功能: 在拍摄人脸前显示引导提示
 *    - 交互: 包含"Go"按钮，点击后启动前置摄像头
 *    - 样式: 全屏弹窗，包含引导图示和文字说明
 * 
 * 3. IDInfoEditorViewController (身份信息编辑)
 *    - 文件: IDInfoEditorViewController.h/.m
 *    - 功能: 展示服务器识别的身份信息并支持编辑
 *    - 字段: 姓名、证件号码、生日等(由服务器返回确定)
 *    - 交互: 支持文本编辑，生日字段点击调用日期选择器
 *    - 样式: 模态弹窗，自定义背景和编辑框样式
 *
 * 4. DatePickerViewController (日期选择器)
 *    - 文件: DatePickerViewController.h/.m
 *    - 功能: 自定义生日选择界面
 *    - 交互: 年/月/日三级联动选择，确认和取消按钮
 *    - 样式: 使用应用统一背景，支持滚轮选择
 *
 * 实现优先级:
 * 1. IDInfoEditorViewController - 核心业务流程的关键组件
 * 2. DatePickerViewController - 用于身份信息中的生日选择
 * 3. FaceGuideViewController - 改善人脸识别体验
 * 4. PhotoSourcePickerView - 优化用户交互
 */

#pragma mark - 权限检查工具
- (void)checkCameraAuthorization:(void (^)(BOOL granted))completion {
    AVAuthorizationStatus status = [AVCaptureDevice authorizationStatusForMediaType:AVMediaTypeVideo];
    switch (status) {
        case AVAuthorizationStatusAuthorized:
            completion(YES);
            break;
        case AVAuthorizationStatusNotDetermined: {
            [AVCaptureDevice requestAccessForMediaType:AVMediaTypeVideo completionHandler:^(BOOL granted) {
                dispatch_async(dispatch_get_main_queue(), ^{
                    completion(granted);
                });
            }];
        } break;
        default:
            completion(NO);
            break;
    }
}

- (void)checkPhotoLibraryAuthorization:(void (^)(BOOL granted))completion {
    PHAuthorizationStatus status;
    if (@available(iOS 14, *)) {
        status = [PHPhotoLibrary authorizationStatusForAccessLevel:PHAccessLevelReadWrite];
    } else {
        status = [PHPhotoLibrary authorizationStatus];
    }
    switch (status) {
        case PHAuthorizationStatusAuthorized:
        case PHAuthorizationStatusLimited:
            completion(YES);
            break;
        case PHAuthorizationStatusNotDetermined: {
            if (@available(iOS 14, *)) {
                [PHPhotoLibrary requestAuthorizationForAccessLevel:PHAccessLevelReadWrite handler:^(PHAuthorizationStatus statusReq) {
                    dispatch_async(dispatch_get_main_queue(), ^{
                        completion(statusReq == PHAuthorizationStatusAuthorized || statusReq == PHAuthorizationStatusLimited);
                    });
                }];
            } else {
                [PHPhotoLibrary requestAuthorization:^(PHAuthorizationStatus statusReq) {
                    dispatch_async(dispatch_get_main_queue(), ^{
                        completion(statusReq == PHAuthorizationStatusAuthorized);
                    });
                }];
            }
        } break;
        default:
            completion(NO);
            break;
    }
}

- (void)showPermissionAlertWithTitle:(NSString *)title message:(NSString *)message {
    UIAlertController *alert = [UIAlertController alertControllerWithTitle:title
                                                                   message:message
                                                            preferredStyle:UIAlertControllerStyleAlert];
    UIAlertAction *settingsAction = [UIAlertAction actionWithTitle:@"Settings" style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        NSURL *url = [NSURL URLWithString:UIApplicationOpenSettingsURLString];
        if ([[UIApplication sharedApplication] canOpenURL:url]) {
            [[UIApplication sharedApplication] openURL:url options:@{} completionHandler:nil];
        }
    }];
    [alert addAction:[UIAlertAction actionWithTitle:@"Cancel" style:UIAlertActionStyleCancel handler:nil]];
    [alert addAction:settingsAction];
    [self presentViewController:alert animated:YES completion:nil];
}

#pragma mark - UIImagePicker Helpers
- (void)presentImagePickerWithSource:(UIImagePickerControllerSourceType)sourceType {
    UIImagePickerController *picker = [[UIImagePickerController alloc] init];
    picker.sourceType = sourceType;
    if (sourceType == UIImagePickerControllerSourceTypeCamera) {
        picker.cameraDevice = UIImagePickerControllerCameraDeviceRear;
    }
    picker.delegate = self;
    picker.allowsEditing = NO;
    [self presentViewController:picker animated:YES completion:nil];
}

#pragma mark - 图片压缩
- (NSData *)compressImageToMaxSize:(UIImage *)image maxSizeKB:(CGFloat)maxSizeKB {
    if (!image) return nil;
    
    // 最大字节数
    CGFloat maxBytes = maxSizeKB * 1024;
    
    // 首先尝试调整图片尺寸
    UIImage *resizedImage = image;
    CGFloat width = image.size.width;
    CGFloat height = image.size.height;
    
    // 如果图片太大，先按比例缩小
    if (width > 1600 || height > 1600) {
        CGFloat scale = MIN(1600 / width, 1600 / height);
        width = width * scale;
        height = height * scale;
        
        UIGraphicsBeginImageContext(CGSizeMake(width, height));
        [image drawInRect:CGRectMake(0, 0, width, height)];
        resizedImage = UIGraphicsGetImageFromCurrentImageContext();
        UIGraphicsEndImageContext();
    }
    
    // 尝试不同的压缩质量，从0.9开始逐渐降低
    CGFloat compression = 0.9;
    NSData *imageData = UIImageJPEGRepresentation(resizedImage, compression);
    
    // 如果第一次压缩就小于目标大小，直接返回
    if (imageData.length <= maxBytes) {
        NSLog(@"图片压缩完成，大小: %.2f KB", imageData.length / 1024.0);
        return imageData;
    }
    
    // 二分法查找合适的压缩比例
    CGFloat max = 0.9;
    CGFloat min = 0.1;
    
    for (int i = 0; i < 6; i++) {
        compression = (max + min) / 2;
        imageData = UIImageJPEGRepresentation(resizedImage, compression);
        
        if (imageData.length < maxBytes * 0.9) {
            min = compression;
        } else if (imageData.length > maxBytes) {
            max = compression;
        } else {
            break;
        }
    }
    
    // 如果二分法后仍然太大，继续降低质量
    if (imageData.length > maxBytes) {
        compression = 0.1;
        imageData = UIImageJPEGRepresentation(resizedImage, compression);
    }
    
    // 如果质量压缩到最低仍然太大，则继续缩小尺寸
    if (imageData.length > maxBytes) {
        CGFloat scale = 0.9;
        while (imageData.length > maxBytes && scale > 0.1) {
            width *= scale;
            height *= scale;
            
            UIGraphicsBeginImageContext(CGSizeMake(width, height));
            [resizedImage drawInRect:CGRectMake(0, 0, width, height)];
            resizedImage = UIGraphicsGetImageFromCurrentImageContext();
            UIGraphicsEndImageContext();
            
            imageData = UIImageJPEGRepresentation(resizedImage, compression);
            scale -= 0.1;
        }
    }
    
    NSLog(@"图片压缩完成，大小: %.2f KB", imageData.length / 1024.0);
    return imageData;
}

@end



/*
保存用户身份证信息（第一项 身份信息确认弹窗 点击后调用）
请求方式 POST from-data 表单提交
请求地址 "/Alicia/girlswere":
场景 识别身份证图片后保存信息项（第一项）
请求参数
{
"early": "23/11/1993", //生日
"drove": "623099344112", //证件号码
"excitedbecause": "NAVEEN TOM VARGHESE", //姓名
"subject": "11", //图片类型 10:人像,11证件正面 
"withcolour": "UMID" //证件类型
}

返回结果，只需要看状态码成功即可走其他逻辑，不需要看返回值
{
"modest": "0",
"patted": "success",
"awkward": {}
}
*/
